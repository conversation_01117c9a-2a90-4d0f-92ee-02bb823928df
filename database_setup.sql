-- KelvinKMS.com Database Setup
-- Create database and tables for user management, view counter, and orders

CREATE DATABASE IF NOT EXISTS kelvinkms;
USE kelvinkms;

-- Users table for authentication with extended profile
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    nickname <PERSON><PERSON><PERSON><PERSON>(50),
    gender ENUM('male', 'female', 'other', 'prefer_not_to_say') DEFAULT 'prefer_not_to_say',
    birthday DATE,
    language VARCHAR(10) DEFAULT 'en',
    email VARCHAR(100),
    phone_number VA<PERSON>HAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_username (username),
    INDEX idx_email (email)
);

-- View counter table for tracking page views
CREATE TABLE IF NOT EXISTS view_counter (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_name VARCHAR(100) DEFAULT 'index',
    view_count INT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_page (page_name)
);

-- Initialize view counter for index page
INSERT INTO view_counter (page_name, view_count) VALUES ('index', 0) 
ON DUPLICATE KEY UPDATE view_count = view_count;

-- Orders table for service orders with pricing
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    services JSON NOT NULL,
    notes TEXT,
    budget_range VARCHAR(50),
    estimated_price DECIMAL(10,2) DEFAULT NULL,
    final_price DECIMAL(10,2) DEFAULT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Admin user (password: admin123)
INSERT INTO users (username, password, email, is_active) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', TRUE)
ON DUPLICATE KEY UPDATE password = VALUES(password);

-- Create additional indexes for better performance (if they don't exist)
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
