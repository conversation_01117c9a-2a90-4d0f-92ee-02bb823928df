<?php
/**
 * System Test Script for KelvinKMS.com
 * This script tests database connectivity and basic functionality
 */

require_once 'config.php';
require_once 'functions.php';

echo "<h1>KelvinKMS.com System Test</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test</h2>";
try {
    $test_connection = get_db_connection();
    if ($test_connection) {
        echo "<p class='success'>✓ Database connection successful</p>";
        close_db_connection($test_connection);
    } else {
        echo "<p class='error'>✗ Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Database connection error: " . $e->getMessage() . "</p>";
}

// Test 2: Check if tables exist
echo "<h2>2. Database Tables Test</h2>";
$tables = ['users', 'view_counter', 'orders'];
foreach ($tables as $table) {
    $sql = "SHOW TABLES LIKE '$table'";
    $result = mysqli_query($link, $sql);
    if ($result && mysqli_num_rows($result) > 0) {
        echo "<p class='success'>✓ Table '$table' exists</p>";
    } else {
        echo "<p class='error'>✗ Table '$table' missing</p>";
    }
}

// Test 3: View Counter Function
echo "<h2>3. View Counter Test</h2>";
try {
    $count = get_current_view_count('test');
    echo "<p class='info'>Current test page view count: $count</p>";
    
    $new_count = get_and_update_view_count('test');
    echo "<p class='success'>✓ View counter updated to: $new_count</p>";
} catch (Exception $e) {
    echo "<p class='error'>✗ View counter error: " . $e->getMessage() . "</p>";
}

// Test 4: User Functions
echo "<h2>4. User Validation Test</h2>";
$test_usernames = ['test123', 'ab', 'valid_user', 'user@invalid'];
foreach ($test_usernames as $username) {
    $valid = validate_username($username);
    $status = $valid ? 'valid' : 'invalid';
    $class = $valid ? 'success' : 'error';
    echo "<p class='$class'>Username '$username': $status</p>";
}

$test_passwords = ['123', 'validpass', 'short'];
foreach ($test_passwords as $password) {
    $valid = validate_password($password);
    $status = $valid ? 'valid' : 'invalid';
    $class = $valid ? 'success' : 'error';
    echo "<p class='$class'>Password '$password': $status</p>";
}

// Test 5: Check Admin User
echo "<h2>5. Admin User Test</h2>";
$sql = "SELECT username FROM users WHERE username = 'admin'";
$result = mysqli_query($link, $sql);
if ($result && mysqli_num_rows($result) > 0) {
    echo "<p class='success'>✓ Admin user exists</p>";
} else {
    echo "<p class='error'>✗ Admin user not found</p>";
    echo "<p class='info'>Creating admin user...</p>";
    
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $sql = "INSERT INTO users (username, password, email, is_active) VALUES ('admin', ?, '<EMAIL>', 1)";
    $stmt = execute_query($link, $sql, "s", [$admin_password]);
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo "<p class='success'>✓ Admin user created successfully</p>";
    } else {
        echo "<p class='error'>✗ Failed to create admin user</p>";
    }
}

// Test 6: File Permissions
echo "<h2>6. File Permissions Test</h2>";
$files_to_check = [
    'index.php', 'config.php', 'functions.php', 'register.php', 
    'login.php', 'member.php', 'admin.php', 'logout.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<p class='success'>✓ $file is readable</p>";
        } else {
            echo "<p class='error'>✗ $file is not readable</p>";
        }
    } else {
        echo "<p class='error'>✗ $file does not exist</p>";
    }
}

// Test 7: Session Test
echo "<h2>7. Session Test</h2>";
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
$_SESSION['test'] = 'session_working';
if (isset($_SESSION['test']) && $_SESSION['test'] === 'session_working') {
    echo "<p class='success'>✓ Sessions are working</p>";
    unset($_SESSION['test']);
} else {
    echo "<p class='error'>✗ Sessions are not working</p>";
}

// Test 8: JSON Response Test
echo "<h2>8. JSON Response Test</h2>";
$test_data = ['success' => true, 'message' => 'Test message'];
$json = json_encode($test_data);
$decoded = json_decode($json, true);
if ($decoded && $decoded['success'] === true) {
    echo "<p class='success'>✓ JSON encoding/decoding works</p>";
} else {
    echo "<p class='error'>✗ JSON encoding/decoding failed</p>";
}

echo "<h2>Test Complete</h2>";
echo "<p class='info'>If all tests pass, your system should be ready to use!</p>";
echo "<p class='info'>Next steps:</p>";
echo "<ul>";
echo "<li>Visit <a href='index.php'>index.php</a> to test the main site</li>";
echo "<li>Try registering a new user</li>";
echo "<li>Test login with admin credentials (admin/admin123)</li>";
echo "<li>Place a test order as a regular user</li>";
echo "<li>Check the admin panel for order management</li>";
echo "</ul>";

close_db_connection($link);
?>
