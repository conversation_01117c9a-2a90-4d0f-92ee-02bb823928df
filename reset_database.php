<?php
/**
 * Database Reset Script
 * WARNING: This will delete all data and recreate the database
 */

require_once 'config.php';

echo "<h1>Database Reset Script</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} .info{color:blue;}</style>";

echo "<p class='warning'><strong>WARNING:</strong> This script will delete all existing data!</p>";

// Drop existing tables
echo "<h2>Dropping Existing Tables...</h2>";
$tables_to_drop = ['orders', 'view_counter', 'users'];

foreach ($tables_to_drop as $table) {
    $sql = "DROP TABLE IF EXISTS $table";
    if (mysqli_query($link, $sql)) {
        echo "<p class='success'>✓ Dropped table: $table</p>";
    } else {
        echo "<p class='error'>✗ Failed to drop table $table: " . mysqli_error($link) . "</p>";
    }
}

// Recreate tables by running the setup script
echo "<h2>Recreating Tables...</h2>";

$sql_file = 'database_setup.sql';
if (!file_exists($sql_file)) {
    echo "<p class='error'>Error: database_setup.sql file not found!</p>";
    exit;
}

$sql_content = file_get_contents($sql_file);
$sql_statements = explode(';', $sql_content);

$success_count = 0;
$error_count = 0;

foreach ($sql_statements as $statement) {
    $statement = trim($statement);
    if (empty($statement) || strpos($statement, '--') === 0 || strlen($statement) < 5) {
        continue; // Skip empty lines, comments, and very short statements
    }
    
    // Skip database creation and use statements
    if (strpos($statement, 'CREATE DATABASE') !== false || strpos($statement, 'USE ') !== false) {
        continue;
    }
    
    if (mysqli_query($link, $statement)) {
        $success_count++;
        echo "<p class='success'>✓ Executed: " . substr($statement, 0, 50) . "...</p>";
    } else {
        $error = mysqli_error($link);
        $error_count++;
        echo "<p class='error'>✗ Failed: " . substr($statement, 0, 50) . "... Error: " . $error . "</p>";
    }
}

echo "<h2>Reset Summary</h2>";
echo "<p class='info'>Successful statements: $success_count</p>";
echo "<p class='info'>Failed statements: $error_count</p>";

if ($error_count == 0) {
    echo "<p class='success'><strong>Database reset completed successfully!</strong></p>";
    echo "<p class='info'>You can now:</p>";
    echo "<ul>";
    echo "<li><a href='test_system.php'>Run system tests</a></li>";
    echo "<li><a href='index.php'>Visit the main site</a></li>";
    echo "<li>Login as admin (username: admin, password: admin123)</li>";
    echo "</ul>";
} else {
    echo "<p class='error'><strong>Database reset completed with errors. Please check the error messages above.</strong></p>";
}

mysqli_close($link);
?>
