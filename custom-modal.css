/* Custom Modal Styles for Beautiful Notifications */
.custom-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    animation: fadeIn 0.3s ease-in-out;
}

.custom-modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 15% auto;
    padding: 0;
    border: none;
    width: 90%;
    max-width: 400px;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    animation: slideIn 0.4s ease-out;
    overflow: hidden;
}

.custom-modal-header {
    padding: 25px 30px 15px;
    text-align: center;
    color: white;
}

.custom-modal-header h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.custom-modal-body {
    padding: 15px 30px 30px;
    text-align: center;
    color: white;
}

.custom-modal-body p {
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
}

.custom-modal-footer {
    padding: 0 30px 30px;
    text-align: center;
}

.custom-modal-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin: 0 5px;
}

.custom-modal-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.custom-modal-btn.primary {
    background: #4CAF50;
    border-color: #4CAF50;
}

.custom-modal-btn.primary:hover {
    background: #45a049;
    border-color: #45a049;
}

.custom-modal-btn.danger {
    background: #f44336;
    border-color: #f44336;
}

.custom-modal-btn.danger:hover {
    background: #da190b;
    border-color: #da190b;
}

.custom-modal-btn.warning {
    background: #ff9800;
    border-color: #ff9800;
}

.custom-modal-btn.warning:hover {
    background: #e68900;
    border-color: #e68900;
}

/* Success Modal */
.custom-modal.success .custom-modal-content {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

/* Error Modal */
.custom-modal.error .custom-modal-content {
    background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
}

/* Warning Modal */
.custom-modal.warning .custom-modal-content {
    background: linear-gradient(135deg, #ff9800 0%, #e68900 100%);
}

/* Info Modal */
.custom-modal.info .custom-modal-content {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
}

/* Confirmation Modal */
.custom-modal.confirm .custom-modal-content {
    background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Close button */
.custom-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.custom-modal-close:hover {
    color: white;
}

/* Order Status Styles */
.order-status {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 18px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.order-status.pending {
    background: #989329;
    color: #ffbf00;
    border: 1px solid #ffeaa7;
}

.order-status.processing {
    background: #2a6771;
    color: #09c1e2;
    border: 1px solid #bee5eb;
}

.order-status.completed {
    background: #428652;
    color: #06e83b;
    border: 1px solid #c3e6cb;
}

.order-status.cancelled {
    background: #e67d86;
    color: #e91227;
    border: 1px solid #f5c6cb;
}

/* Order Card Styles */
.order-card {
    background: #1eada3;
    border-radius: 14px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    color: #ffffff;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.order-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.order-card h4 {
    margin-top: 0;
    color: #ffffff;
    font-size: 24px;
}

.order-actions {
    margin-top: 15px;
    text-align: right;
}

.order-actions button {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s ease;
}

.order-actions button:hover {
    background: #c0392b;
}

.order-actions button:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

/* Loading spinner */
.loading-spinner {
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 3px solid white;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 10px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
