<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: index.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Area - KelvinKMS.com</title>
    <link rel="stylesheet" href="custom-modal.css" />
    <style>
        body { font-family: Arial, sans-serif; background-color: #a48f19; color: white; margin: 0; padding: 20px; }
        .container { max-width: 1000px; margin: auto; background-color: #2b9869; padding: 30px; border-radius: 14px; box-shadow: 0 5px 15px rgba(0,0,0,0.5); }
        h1, h2 { color: #00cyan; text-align: center; }
        #welcome-msg { text-align: center; font-size: 24px; margin-bottom: 20px; }
        .order-form { margin-top: 20px; }
        .service-item { background-color: #2266a8; padding: 15px; border-radius: 12px; margin-bottom: 15px; display: flex; align-items: center; }
        .service-item input { margin-right: 15px; width: 20px; height: 20px; }
        .service-item label { font-size: 18px; flex-grow: 1; }
        #order-notes { width: calc(100% - 22px); height: 80px; padding: 10px; margin-top: 10px; border-radius: 12px; background-color: #1f5478; color: white; border: 2px solid #1585cf; font-size: 24px; }
        button { width: 200px; padding: 15px; border: none; border-radius: 12px; background-color:rgb(232, 186, 0); color: white; font-size: 22px; text-align: center; cursor: pointer; margin-top: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.5);}
        button:hover { background-color: #20B2AA; }

        /* Logout button positioning */
        .logout-btn {
            position: fixed;
            bottom: 25px;
            right: 35px;
            width: 100px;
            background-color:rgb(255, 25, 0);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 22px;
            cursor: pointer;
            font-size: 18px;
            font-weight: 500;
            z-index: 1000;
            transition: background-color 0.3s ease;
        }
        .logout-btn:hover { background-color: #c0392b; }

        /* Budget selection styles */
        .budget-selection {
            margin-top: 10px;
            padding: 10px;
            background-color: #1585cf;
            border-radius: 12px;
            display: none;
        }
        .budget-selection select {
            width: 100%;
            padding: 8px;
            border-radius: 12px;
            background-color: #1585cf;
            color: white;
            border: 1px solid #1585cf;
        }
    </style>
</head>
<body>
    <!-- Logout button in top right -->
    <button class="logout-btn" id="logoutBtn">Logout</button>

    <div class="container">
        <h1 id="welcome-msg">Welcome, <?php echo htmlspecialchars($_SESSION["username"]); ?>!</h1>
        <h2>Service Orders</h2>
        <p style="text-align: center;">Please select the services you need and provide detailed information in the notes, then click to place your order.</p>
        
        <form id="orderForm" class="order-form">
            <div class="service-item">
                <input type="checkbox" id="service1" name="service" value="VIP Custom PC" onchange="toggleBudgetSelection(this)">
                <label for="service1">VIP Custom PC</label>
                <div class="budget-selection" id="budgetSelection">
                    <label for="budgetRange">Budget Range:</label>
                    <select id="budgetRange" name="budget_range">
                        <option value="">Select Budget Range</option>
                        <option value="2000_3000">$2,000 - $3,000</option>
                        <option value="3000_4000">$3,000 - $4,000</option>
                        <option value="4000_5000">$4,000 - $5,000</option>
                        <option value="5000_6000">$5,000 - $6,000</option>
                        <option value="6000_7000">$6,000 - $7,000</option>
                        <option value="7000_8000">$7,000 - $8,000</option>
                        <option value="custom">Custom Budget (specify in notes)</option>
                    </select>
                </div>
            </div>
            <div class="service-item">
                <input type="checkbox" id="service2" name="service" value="Optimize Photo & Video">
                <label for="service2">Optimize Photo & Video</label>
            </div>
            <div class="service-item">
                <input type="checkbox" id="service3" name="service" value="Print Service">
                <label for="service3">Print Service</label>
            </div>
            <div class="service-item">
                <input type="checkbox" id="service4" name="service" value="All in 1 Website Service">
                <label for="service4">All in 1 Website Service</label>
            </div>
             <div class="service-item">
                <input type="checkbox" id="service5" name="service" value="Question Consult">
                <label for="service5">Question Consult</label>
            </div>
            <div class="service-item">
                <input type="checkbox" id="service6" name="service" value="Useful PC Softwares & iOS Apps">
                <label for="service6">Useful PC Softwares & iOS Apps</label>
            </div>
            <div class="service-item">
                <input type="checkbox" id="service7" name="service" value="Best Music Player">
                <label for="service7">Best Music Player</label>
            </div>
            
            <textarea id="order-notes" placeholder="Please enter your detailed requirements here"></textarea>

            <button type="submit">Confirm Order</button>
        </form>

        <!-- Order History Section -->
        <div id="orderHistory" style="margin-top: 30px;">
            <h2>Your Orders</h2>
            <div id="ordersList">
                <!-- Orders will be loaded here -->
            </div>
        </div>
    </div>

    <script src="custom-modal.js"></script>
    <script>
        // Toggle budget selection for VIP Custom PC
        function toggleBudgetSelection(checkbox) {
            const budgetSelection = document.getElementById('budgetSelection');
            if (checkbox.checked) {
                budgetSelection.style.display = 'block';
            } else {
                budgetSelection.style.display = 'none';
                document.getElementById('budgetRange').value = '';
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            // User is already authenticated via PHP session
            loadUserOrders();

            document.getElementById('orderForm').addEventListener('submit', (e) => {
                e.preventDefault();

                const selectedServices = Array.from(document.querySelectorAll('input[name="service"]:checked'))
                                              .map(checkbox => checkbox.value);

                if (selectedServices.length === 0) {
                    showError('No Service Selected', 'Please select at least one service before placing your order.');
                    return;
                }

                const notes = document.getElementById('order-notes').value;
                const budgetRange = document.getElementById('budgetRange').value;

                // Check if VIP Custom PC is selected but no budget is chosen
                if (selectedServices.includes('VIP Custom PC') && !budgetRange) {
                    showError('Budget Required', 'Please select a budget range for VIP Custom PC service.');
                    return;
                }

                // Show confirmation dialog
                showConfirm(
                    'Confirm Order',
                    `Are you sure you want to place this order for ${selectedServices.length} service(s)?`,
                    () => {
                        // User confirmed, proceed with order
                        showLoading('Submitting Order', 'Please wait while we process your order...');

                        const formData = new FormData();
                        formData.append('services', JSON.stringify(selectedServices));
                        formData.append('notes', notes);
                        formData.append('budget_range', budgetRange);

                        fetch('submit_order.php', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            hideModal();
                            if (data.success) {
                                showSuccess('Order Submitted', 'Your order has been submitted successfully! We will contact you soon.', () => {
                                    e.target.reset(); // Clear the form
                                    loadUserOrders(); // Refresh orders
                                });
                            } else {
                                showError('Order Failed', data.message || 'Failed to submit order. Please try again.');
                            }
                        })
                        .catch(error => {
                            hideModal();
                            console.error('Error:', error);
                            showError('Order Failed', 'Network error. Please check your connection and try again.');
                        });
                    }
                );
            });

            document.getElementById('logoutBtn').addEventListener('click', () => {
                showConfirm(
                    'Confirm Logout',
                    'Are you sure you want to logout? You will need to login again to access your account.',
                    () => {
                        showLoading('Logging Out', 'Please wait...');
                        setTimeout(() => {
                            window.location.href = 'logout.php';
                        }, 1000);
                    }
                );
            });
        });

        // Load user orders
        function loadUserOrders() {
            fetch('get_user_orders.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayOrders(data.orders);
                    } else {
                        document.getElementById('ordersList').innerHTML = '<p>No orders found.</p>';
                    }
                })
                .catch(error => {
                    console.error('Error loading orders:', error);
                    document.getElementById('ordersList').innerHTML = '<p>Error loading orders.</p>';
                });
        }

        // Display orders
        function displayOrders(orders) {
            const ordersList = document.getElementById('ordersList');

            if (orders.length === 0) {
                ordersList.innerHTML = '<p>You have not placed any orders yet.</p>';
                return;
            }

            let ordersHTML = '';
            orders.forEach(order => {
                const services = JSON.parse(order.services);
                const canCancel = order.status === 'pending';

                let priceInfo = '';
                if (order.estimated_price) {
                    priceInfo += `<p><strong>Estimated Price:</strong> $${parseFloat(order.estimated_price).toFixed(2)}</p>`;
                }
                if (order.final_price) {
                    priceInfo += `<p><strong>Final Price:</strong> $${parseFloat(order.final_price).toFixed(2)}</p>`;
                }

                let budgetInfo = '';
                if (order.budget_range) {
                    const budgetText = order.budget_range.replace(/_/g, ' ').replace('under', 'Under $').replace('over', 'Over $');
                    budgetInfo = `<p><strong>Budget Range:</strong> ${budgetText}</p>`;
                }

                ordersHTML += `
                    <div class="order-card">
                        <h4>Order #${order.id}</h4>
                        <p><strong>Date:</strong> ${new Date(order.created_at).toLocaleDateString()}</p>
                        <p><strong>Status:</strong> <span class="order-status ${order.status}">${getStatusText(order.status)}</span></p>
                        ${budgetInfo}
                        ${priceInfo}
                        <p><strong>Services:</strong></p>
                        <ul>
                            ${services.map(service => `<li>${service}</li>`).join('')}
                        </ul>
                        <p><strong>Notes:</strong> ${order.notes || 'None'}</p>
                        <div class="order-actions">
                            ${canCancel ? `<button onclick="cancelOrder(${order.id})">Cancel Order</button>` : ''}
                        </div>
                    </div>
                `;
            });

            ordersList.innerHTML = ordersHTML;
        }

        // Get status text with better descriptions
        function getStatusText(status) {
            const statusTexts = {
                'pending': 'Order Received',
                'processing': 'Order in Progress',
                'completed': 'Order Completed',
                'cancelled': 'Order Cancelled'
            };
            return statusTexts[status] || status;
        }

        // Cancel order function
        function cancelOrder(orderId) {
            showConfirm(
                'Cancel Order',
                'Are you sure you want to cancel this order? This action cannot be undone.',
                () => {
                    showLoading('Cancelling Order', 'Please wait...');

                    const formData = new FormData();
                    formData.append('order_id', orderId);

                    fetch('cancel_order.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideModal();
                        if (data.success) {
                            showSuccess('Order Cancelled', 'Your order has been cancelled successfully.', () => {
                                loadUserOrders(); // Refresh orders
                            });
                        } else {
                            showError('Cancellation Failed', data.message || 'Failed to cancel order.');
                        }
                    })
                    .catch(error => {
                        hideModal();
                        console.error('Error:', error);
                        showError('Cancellation Failed', 'Network error. Please try again.');
                    });
                }
            );
        }

        // Auto-refresh orders every 10 seconds to get real-time updates
        setInterval(loadUserOrders, 10000);
    </script>

</body>
</html>