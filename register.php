<?php
header('Content-Type: application/json');
require_once 'config.php';
require_once 'functions.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get and sanitize all form data
    $first_name = sanitize_input($_POST['first_name']);
    $last_name = sanitize_input($_POST['last_name']);
    $nickname = sanitize_input($_POST['nickname']);
    $username = sanitize_input($_POST['username']);
    $gender = sanitize_input($_POST['gender']);
    $birthday = sanitize_input($_POST['birthday']);
    $language = sanitize_input($_POST['language']);
    $email = sanitize_input($_POST['email']);
    $phone_number = sanitize_input($_POST['phone_number']);
    $password = $_POST['password'];

    // Validation
    if (empty($first_name) || empty($last_name)) {
        echo json_encode([
            'success' => false,
            'message' => 'First name and last name are required.'
        ]);
        exit;
    }

    if (!validate_username($username)) {
        echo json_encode([
            'success' => false,
            'message' => 'Username must be 3-20 characters long and contain only letters, numbers, and underscores.'
        ]);
        exit;
    }

    if (!in_array($gender, ['male', 'female', 'other', 'prefer_not_to_say'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Please select a valid gender option.'
        ]);
        exit;
    }

    if (empty($birthday)) {
        echo json_encode([
            'success' => false,
            'message' => 'Birthday is required.'
        ]);
        exit;
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode([
            'success' => false,
            'message' => 'Please enter a valid email address.'
        ]);
        exit;
    }

    if (empty($phone_number)) {
        echo json_encode([
            'success' => false,
            'message' => 'Phone number is required.'
        ]);
        exit;
    }

    if (!validate_password($password)) {
        echo json_encode([
            'success' => false,
            'message' => 'Password must be at least 6 characters long.'
        ]);
        exit;
    }

    // Check if username or email already exists
    $check_sql = "SELECT id FROM users WHERE username = ? OR email = ?";
    $check_stmt = execute_query($link, $check_sql, "ss", [$username, $email]);

    if ($check_stmt) {
        $result = mysqli_stmt_get_result($check_stmt);
        if (mysqli_fetch_assoc($result)) {
            mysqli_stmt_close($check_stmt);
            echo json_encode([
                'success' => false,
                'message' => 'Username or email already exists. Please choose different credentials.'
            ]);
            exit;
        }
        mysqli_stmt_close($check_stmt);
    }

    // Hash password and insert user with all profile data
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    $insert_sql = "INSERT INTO users (first_name, last_name, nickname, username, gender, birthday, language, email, phone_number, password) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $insert_stmt = execute_query($link, $insert_sql, "ssssssssss", [$first_name, $last_name, $nickname, $username, $gender, $birthday, $language, $email, $phone_number, $hashed_password]);

    if ($insert_stmt) {
        mysqli_stmt_close($insert_stmt);
        echo json_encode([
            'success' => true,
            'message' => 'Registration successful!'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Registration failed. Please try again later.'
        ]);
    }

    close_db_connection($link);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
}
?>