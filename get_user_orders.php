<?php
session_start();
header('Content-Type: application/json');
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    echo json_encode([
        'success' => false,
        'message' => 'Please login first.'
    ]);
    exit;
}

try {
    $user_id = $_SESSION["id"];
    
    // Get user orders
    $sql = "SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC";
    $stmt = execute_query($link, $sql, "i", [$user_id]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $orders = [];
        
        while ($row = mysqli_fetch_assoc($result)) {
            $orders[] = $row;
        }
        
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'success' => true,
            'orders' => $orders
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to fetch orders.'
        ]);
    }
    
    close_db_connection($link);
} catch (Exception $e) {
    error_log("Error in get_user_orders.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while fetching orders.'
    ]);
}
?>
