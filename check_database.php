<?php
/**
 * Quick Database Status Check
 */

require_once 'config.php';

echo "<h1>Database Status Check</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} table{border-collapse:collapse;width:100%;margin:10px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background-color:#f2f2f2;}</style>";

// Check database connection
echo "<h2>Database Connection</h2>";
if ($link) {
    echo "<p class='success'>✓ Database connected successfully</p>";
} else {
    echo "<p class='error'>✗ Database connection failed</p>";
    exit;
}

// Check tables exist
echo "<h2>Tables Status</h2>";
$tables = ['users', 'view_counter', 'orders'];
foreach ($tables as $table) {
    $result = mysqli_query($link, "SHOW TABLES LIKE '$table'");
    if ($result && mysqli_num_rows($result) > 0) {
        echo "<p class='success'>✓ Table '$table' exists</p>";
    } else {
        echo "<p class='error'>✗ Table '$table' missing</p>";
    }
}

// Check users table data
echo "<h2>Users Table Data</h2>";
$result = mysqli_query($link, "SELECT id, username, first_name, last_name, email, created_at FROM users ORDER BY id");
if ($result) {
    echo "<table>";
    echo "<tr><th>ID</th><th>Username</th><th>First Name</th><th>Last Name</th><th>Email</th><th>Created</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['username']) . "</td>";
        echo "<td>" . htmlspecialchars($row['first_name'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['last_name'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['email'] ?? '') . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='error'>✗ Failed to query users table</p>";
}

// Check orders table data
echo "<h2>Orders Table Data</h2>";
$result = mysqli_query($link, "SELECT id, username, services, budget_range, estimated_price, final_price, status, created_at FROM orders ORDER BY id");
if ($result) {
    if (mysqli_num_rows($result) > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>Username</th><th>Services</th><th>Budget</th><th>Est. Price</th><th>Final Price</th><th>Status</th><th>Created</th></tr>";
        while ($row = mysqli_fetch_assoc($result)) {
            $services = json_decode($row['services'], true);
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['username']) . "</td>";
            echo "<td>" . htmlspecialchars(implode(', ', $services)) . "</td>";
            echo "<td>" . htmlspecialchars($row['budget_range'] ?? 'N/A') . "</td>";
            echo "<td>$" . ($row['estimated_price'] ?? 'N/A') . "</td>";
            echo "<td>$" . ($row['final_price'] ?? 'N/A') . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='info'>No orders found (this is normal for a fresh setup)</p>";
    }
} else {
    echo "<p class='error'>✗ Failed to query orders table</p>";
}

// Check view counter
echo "<h2>View Counter Status</h2>";
$result = mysqli_query($link, "SELECT * FROM view_counter");
if ($result) {
    echo "<table>";
    echo "<tr><th>Page</th><th>View Count</th><th>Last Updated</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['page_name']) . "</td>";
        echo "<td>" . $row['view_count'] . "</td>";
        echo "<td>" . $row['last_updated'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='error'>✗ Failed to query view counter table</p>";
}

// Test accounts summary
echo "<h2>Test Accounts Summary</h2>";
echo "<div style='background-color:#e8f5e8;padding:15px;border-radius:8px;'>";
echo "<h3>Available Test Accounts:</h3>";
echo "<p><strong>Admin Account:</strong></p>";
echo "<ul><li>Username: <code>admin</code></li><li>Password: <code>admin123</code></li><li>Access: Admin dashboard with full privileges</li></ul>";

echo "<p><strong>Test Member Account:</strong></p>";
echo "<ul><li>Username: <code>testuser</code></li><li>Password: <code>test123</code></li><li>Access: Member area with order capabilities</li></ul>";
echo "</div>";

// Quick links
echo "<h2>Quick Actions</h2>";
echo "<div style='background-color:#f0f8ff;padding:15px;border-radius:8px;'>";
echo "<p><strong>Test the System:</strong></p>";
echo "<ul>";
echo "<li><a href='index.php' target='_blank'>🏠 Visit Main Site</a></li>";
echo "<li><a href='index.php' target='_blank'>👤 Test Login (use accounts above)</a></li>";
echo "<li><a href='test_system.php' target='_blank'>🧪 Run System Tests</a></li>";
echo "</ul>";
echo "</div>";

mysqli_close($link);
?>
