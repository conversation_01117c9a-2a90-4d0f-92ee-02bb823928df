<?php
session_start();
header('Content-Type: application/json');
require_once 'config.php';
require_once 'functions.php';

// Check if user is logged in
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    echo json_encode([
        'success' => false,
        'message' => 'Please login first.'
    ]);
    exit;
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $user_id = $_SESSION["id"];
    $order_id = (int)$_POST['order_id'];
    
    try {
        // First, check if the order belongs to the user and can be cancelled
        $check_sql = "SELECT status FROM orders WHERE id = ? AND user_id = ?";
        $check_stmt = execute_query($link, $check_sql, "ii", [$order_id, $user_id]);
        
        if ($check_stmt) {
            $result = mysqli_stmt_get_result($check_stmt);
            $order = mysqli_fetch_assoc($result);
            mysqli_stmt_close($check_stmt);
            
            if (!$order) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Order not found or access denied.'
                ]);
                exit;
            }
            
            if ($order['status'] !== 'pending') {
                echo json_encode([
                    'success' => false,
                    'message' => 'Only pending orders can be cancelled.'
                ]);
                exit;
            }
            
            // Update order status to cancelled
            $update_sql = "UPDATE orders SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?";
            $update_stmt = execute_query($link, $update_sql, "ii", [$order_id, $user_id]);
            
            if ($update_stmt) {
                $affected_rows = mysqli_stmt_affected_rows($update_stmt);
                mysqli_stmt_close($update_stmt);
                
                if ($affected_rows > 0) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Order cancelled successfully.'
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Failed to cancel order.'
                    ]);
                }
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Database error occurred.'
                ]);
            }
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Database error occurred.'
            ]);
        }
        
        close_db_connection($link);
    } catch (Exception $e) {
        error_log("Error in cancel_order.php: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'An error occurred while cancelling the order.'
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
}
?>
