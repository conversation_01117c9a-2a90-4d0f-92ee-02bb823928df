<?php
session_start();
header('Content-Type: application/json');
require_once 'config.php';
require_once 'functions.php';

// Check if user is logged in
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    echo json_encode([
        'success' => false,
        'message' => 'Please login first.'
    ]);
    exit;
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $user_id = $_SESSION["id"];
    $username = $_SESSION["username"];
    $services = $_POST['services'];
    $notes = sanitize_input($_POST['notes']);
    $budget_range = isset($_POST['budget_range']) ? sanitize_input($_POST['budget_range']) : '';
    
    // Validate services
    if (empty($services)) {
        echo json_encode([
            'success' => false,
            'message' => 'Please select at least one service.'
        ]);
        exit;
    }
    
    // Decode services JSON
    $services_array = json_decode($services, true);
    if (!$services_array || !is_array($services_array)) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid services data.'
        ]);
        exit;
    }
    
    // Insert order into database
    $sql = "INSERT INTO orders (user_id, username, services, notes, budget_range) VALUES (?, ?, ?, ?, ?)";
    $stmt = execute_query($link, $sql, "issss", [$user_id, $username, $services, $notes, $budget_range]);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode([
            'success' => true,
            'message' => 'Order submitted successfully!'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to submit order. Please try again later.'
        ]);
    }
    
    close_db_connection($link);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
}
?>
