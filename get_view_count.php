<?php
header('Content-Type: application/json');
require_once 'functions.php';

try {
    // Get and update view count
    $count = get_and_update_view_count('index');
    
    echo json_encode([
        'success' => true,
        'count' => $count
    ]);
} catch (Exception $e) {
    error_log("Error in get_view_count.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'count' => 0,
        'message' => 'Failed to get view count'
    ]);
}
?>
