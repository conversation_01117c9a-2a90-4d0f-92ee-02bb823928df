// Custom Modal Functions for Beautiful Notifications

class CustomModal {
    constructor() {
        this.createModalHTML();
    }

    createModalHTML() {
        // Remove existing modal if any
        const existingModal = document.getElementById('customModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal HTML
        const modalHTML = `
            <div id="customModal" class="custom-modal">
                <div class="custom-modal-content">
                    <span class="custom-modal-close">&times;</span>
                    <div class="custom-modal-header">
                        <h3 id="customModalTitle">Title</h3>
                    </div>
                    <div class="custom-modal-body">
                        <p id="customModalMessage">Message</p>
                        <div id="customModalSpinner" class="loading-spinner" style="display: none;"></div>
                    </div>
                    <div class="custom-modal-footer" id="customModalFooter">
                        <button class="custom-modal-btn primary" id="customModalOk">OK</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.bindEvents();
    }

    bindEvents() {
        const modal = document.getElementById('customModal');
        const closeBtn = document.querySelector('.custom-modal-close');
        const okBtn = document.getElementById('customModalOk');

        closeBtn.onclick = () => this.hide();
        okBtn.onclick = () => this.hide();

        window.onclick = (event) => {
            if (event.target === modal) {
                this.hide();
            }
        };
    }

    show(title, message, type = 'info', buttons = null) {
        const modal = document.getElementById('customModal');
        const titleEl = document.getElementById('customModalTitle');
        const messageEl = document.getElementById('customModalMessage');
        const footerEl = document.getElementById('customModalFooter');
        const spinner = document.getElementById('customModalSpinner');

        // Set content
        titleEl.textContent = title;
        messageEl.textContent = message;
        
        // Hide spinner
        spinner.style.display = 'none';

        // Set modal type
        modal.className = `custom-modal ${type}`;

        // Set buttons
        if (buttons) {
            footerEl.innerHTML = buttons;
        } else {
            footerEl.innerHTML = '<button class="custom-modal-btn primary" onclick="customModal.hide()">OK</button>';
        }

        // Show modal
        modal.style.display = 'block';
    }

    showLoading(title, message) {
        const modal = document.getElementById('customModal');
        const titleEl = document.getElementById('customModalTitle');
        const messageEl = document.getElementById('customModalMessage');
        const footerEl = document.getElementById('customModalFooter');
        const spinner = document.getElementById('customModalSpinner');

        titleEl.textContent = title;
        messageEl.textContent = message;
        spinner.style.display = 'block';
        footerEl.innerHTML = '';

        modal.className = 'custom-modal info';
        modal.style.display = 'block';
    }

    hide() {
        const modal = document.getElementById('customModal');
        modal.style.display = 'none';
    }

    confirm(title, message, onConfirm, onCancel = null) {
        const buttons = `
            <button class="custom-modal-btn danger" onclick="customModal.handleConfirm(false)">Cancel</button>
            <button class="custom-modal-btn primary" onclick="customModal.handleConfirm(true)">Confirm</button>
        `;
        
        this.confirmCallback = onConfirm;
        this.cancelCallback = onCancel;
        this.show(title, message, 'confirm', buttons);
    }

    handleConfirm(confirmed) {
        this.hide();
        if (confirmed && this.confirmCallback) {
            this.confirmCallback();
        } else if (!confirmed && this.cancelCallback) {
            this.cancelCallback();
        }
    }

    success(title, message, callback = null) {
        const buttons = callback ? 
            `<button class="custom-modal-btn primary" onclick="customModal.handleCallback()">OK</button>` :
            `<button class="custom-modal-btn primary" onclick="customModal.hide()">OK</button>`;
        
        this.successCallback = callback;
        this.show(title, message, 'success', buttons);
    }

    error(title, message, callback = null) {
        const buttons = callback ? 
            `<button class="custom-modal-btn danger" onclick="customModal.handleCallback()">OK</button>` :
            `<button class="custom-modal-btn danger" onclick="customModal.hide()">OK</button>`;
        
        this.errorCallback = callback;
        this.show(title, message, 'error', buttons);
    }

    warning(title, message, callback = null) {
        const buttons = callback ? 
            `<button class="custom-modal-btn warning" onclick="customModal.handleCallback()">OK</button>` :
            `<button class="custom-modal-btn warning" onclick="customModal.hide()">OK</button>`;
        
        this.warningCallback = callback;
        this.show(title, message, 'warning', buttons);
    }

    handleCallback() {
        this.hide();
        if (this.successCallback) {
            this.successCallback();
            this.successCallback = null;
        } else if (this.errorCallback) {
            this.errorCallback();
            this.errorCallback = null;
        } else if (this.warningCallback) {
            this.warningCallback();
            this.warningCallback = null;
        }
    }
}

// Initialize global modal instance
let customModal;
document.addEventListener('DOMContentLoaded', function() {
    customModal = new CustomModal();
});

// Utility functions for easy access
function showSuccess(title, message, callback = null) {
    customModal.success(title, message, callback);
}

function showError(title, message, callback = null) {
    customModal.error(title, message, callback);
}

function showWarning(title, message, callback = null) {
    customModal.warning(title, message, callback);
}

function showInfo(title, message, callback = null) {
    customModal.show(title, message, 'info');
}

function showConfirm(title, message, onConfirm, onCancel = null) {
    customModal.confirm(title, message, onConfirm, onCancel);
}

function showLoading(title, message) {
    customModal.showLoading(title, message);
}

function hideModal() {
    customModal.hide();
}
