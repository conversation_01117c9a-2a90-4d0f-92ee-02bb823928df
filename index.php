<!DOCTYPE html>
<html lang="en">
<head>
    <title>KelvinKMS.com</title>
    <meta charset="UTF-8" />
    <meta name="robots" content="noarchive" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="author" content="Kelvin KMS" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="icon" type="image/png" sizes="16x16" href="/Favicon/KMS_Logo_16.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/Favicon/KMS_Logo_32.png" />
    <link rel="icon" type="image/png" sizes="96x96" href="/Favicon/KMS_Logo_96.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="/Favicon/KMS_Logo_512.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/Favicon/KMS_Logo_152.png" />
    <link rel="mask-icon" href="/Favicon/KMS_Logo.svg" color="#5bbad5" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="stylesheet" href="custom-modal.css" />
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            font-size: 20px;
            background-color: black;
        }
        .container {
            width: 100%;
            margin: 0 auto;
            text-align: center;
        }
        .section {
            padding: 4px;
            margin: 2px auto;
            border-radius: 16px;
            border: 5px solid;
            color: white;
            text-align: center;
            width: 95%;
            font-size: 20px;
            text-shadow: 2px 2px 4px black;
        }
        #home { background-color: rgb(17, 196, 206); border-color: rgb(30, 131, 208); padding: -1px; height: auto; }
        .nav { position: fixed; right: 10px; top: 50%; transform: translateY(-50%); display: flex; flex-direction: column; }
        .nav button { margin: 2px 0; padding: 6px; cursor: pointer; border-radius: 16px; border: 3px solid; font-size: 20px; }
        .nav button:hover { background-color: rgb(0, 255, 255); }
        .nav button, .mobile-nav button { text-shadow: 1px 1px 3px black; }
        .mobile-nav { display: none; position: fixed; right: 10px; bottom: 10px; }
        .mobile-nav button { padding: 15px; border-radius: 50%; background-color: #444; color: white; border: none; font-size: 18px; cursor: pointer; }
        .slideshow-container { position: relative; max-width: 700px; margin: auto; height: 700px; overflow: hidden; }
        .slide { display: none; width: 100%; height: 100%; object-fit: cover; }
        @media screen and (max-width: 768px) {
            .nav { display: none; }
            .mobile-nav { display: block; }
        }
        #portfolio { background-color: #73c873; border-color: #00ff00; }
        #optimize { background-color: rgb(193, 187, 12); border-color: rgb(236, 232, 12); }
        #print-service { background-color: rgb(215, 185, 11); border-color: rgb(255, 191, 73); }
        #website-service { background-color: #0567a6; border-color: #4682b4; }
        #question-consult { background-color: #ff7500; border-color: #cd853f; }
        #useful-softwares { background-color: #20B2AA; border-color: #008B8B; }
        #best-music { background-color: rgb(193, 14, 202); border-color: rgb(255, 0, 234); }
        #copyright { background-color: #164b7b; border-color: #1667b1; }

        /* New Styles for Counter, Auth Buttons, and Modal */
        .site-info {
            position: fixed;
            top: 15px;
            left: 15px;
            background-color: rgba(0, 0, 0, 0.6);
            padding: 10px 15px;
            border-radius: 10px;
            color: white;
            text-align: left;
            z-index: 1000;
        }
        .view-counter { font-size: 18px; margin-bottom: 10px; }
        .auth-buttons button {
            display: block;
            width: 100%;
            padding: 8px;
            margin-bottom: 5px;
            border-radius: 8px;
            border: 2px solid #00cyan;
            background-color: #008B8B;
            color: white;
            cursor: pointer;
            font-size: 16px;
        }
        .auth-buttons button:hover { background-color: #20B2AA; }

        /* Logout button positioning */
        .logout-btn {
            position: fixed;
            top: 15px;
            right: 15px;
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            z-index: 1000;
            transition: background-color 0.3s ease;
        }
        .logout-btn:hover { background-color: #c0392b; }

        .modal {
            display: none;
            position: fixed;
            z-index: 1001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.7);
        }
        .modal-content {
            background-color: #2c2c2c;
            color: white;
            margin: 5% auto;
            padding: 30px;
            border: 1px solid #888;
            width: 90%;
            max-width: 500px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
            text-align: center;
            max-height: 90vh;
            overflow-y: auto;
        }
        .close-btn {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close-btn:hover, .close-btn:focus { color: #fff; }
        .modal-content input, .modal-content select {
            width: calc(100% - 20px);
            padding: 10px;
            margin: 8px 0;
            border-radius: 5px;
            border: 1px solid #555;
            background-color: #333;
            color: white;
            font-size: 14px;
        }
        .modal-content select option {
            background-color: #333;
            color: white;
        }
        .form-row {
            display: flex;
            gap: 10px;
        }
        .form-row input {
            flex: 1;
        }
        .modal-content button {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background-color: #008B8B;
            color: white;
            font-size: 18px;
            cursor: pointer;
            margin-top: 10px;
        }
        .modal-content button:hover { background-color: #20B2AA; }
    </style>
</head>
<body>
    <!-- Site Info and Authentication -->
    <div class="site-info">
        <div class="view-counter">Page Views: <span id="counter">0</span></div>
        <div class="auth-buttons">
            <button id="loginBtn">Login</button>
            <button id="registerBtn">Register</button>
        </div>
    </div>

    <!-- Register Modal -->
    <div id="registerModal" class="modal">
        <div class="modal-content">
            <span class="close-btn" id="closeRegister">×</span>
            <h2>Create Account</h2>
            <form id="registerForm">
                <div class="form-row">
                    <input type="text" id="regFirstName" placeholder="First Name" required>
                    <input type="text" id="regLastName" placeholder="Last Name" required>
                </div>
                <input type="text" id="regNickname" placeholder="Nickname">
                <input type="text" id="regUsername" placeholder="Username" required>

                <select id="regGender" required>
                    <option value="">Select Gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                    <option value="prefer_not_to_say">Prefer not to say</option>
                </select>

                <input type="date" id="regBirthday" placeholder="Birthday" required>

                <select id="regLanguage" required>
                    <option value="en">English</option>
                    <option value="zh">中文</option>
                    <option value="es">Español</option>
                    <option value="fr">Français</option>
                    <option value="de">Deutsch</option>
                    <option value="ja">日本語</option>
                </select>

                <input type="email" id="regEmail" placeholder="Email Address" required>
                <input type="email" id="regEmailConfirm" placeholder="Confirm Email Address" required>

                <input type="tel" id="regPhone" placeholder="Phone Number" required>

                <input type="password" id="regPassword" placeholder="Password" required>
                <input type="password" id="regPasswordConfirm" placeholder="Confirm Password" required>

                <button type="submit">Register</button>
            </form>
        </div>
    </div>

    <!-- Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <span class="close-btn" id="closeLogin">×</span>
            <h2>Member Login</h2>
            <form id="loginForm">
                <input type="text" id="loginUsername" placeholder="Username" required>
                <input type="password" id="loginPassword" placeholder="Password" required>
                <button type="submit">Login</button>
            </form>
        </div>
    </div>

    <!-- Original page content -->
    <div class="container">
        <!-- ... your existing sections from #home to #copyright ... -->
        <div id="home" class="section">
            <h1>Welcome to<br />KelvinKMS.com</h1>
            <p>Hello! My name is Kelvin.</p>
            <p>I offer some services for you.</p>
            <p>1 : VIP Custom PC</p>
            <p>2 : Optimize Photo & Video</p>
            <p>3 : Print Service</p>
            <p>4 : All in 1 Website Service</p>
            <p>5 : Question Consult</p>
            <p>6 : Useful PC Softwares & iOS Apps</p>
            <p>7 : Best Music Player</p>
        </div>
        <div id="portfolio" class="section">
            <h2>VIP Custom PC</h2>
            <p>The most beautiful custom PC you can expect.</p>
            <p>I make all custom PCs one by one.</p>
            <p>Contact me for details.</p>
        </div>
        <div class="slideshow-container">
            <img class="slide" src="/KMS_VIP_PC/VIP_PC_1.jpg" alt="Slide 1">
            <img class="slide" src="/KMS_VIP_PC/VIP_PC_2.jpg" alt="Slide 2">
        </div>
        <div id="optimize" class="section">
            <h2>Optimize Photo & Video</h2>
            <p>I can optimize your low quality photo or video to the best quality.</p>
            <p>I can remove photo or video watermark.</p>
            <p>One photo optimize = $3</p>
            <p>One photo watermark remove = $3</p>
            <p>Over 10 photos = 10% discount</p>
            <p>One video 1 min 30FPS 1080P = $2</p>
            <p>Video Watermark Remove</p>
            <p>Easy mode = $10 each up to 30 mins</p>
            <p>Hard mode = $30 each up to 30 mins</p>
            <p><strong>Notice :</strong> Price can be changed if I need to adjust.</p>
        </div>
        <div id="print-service" class="section">
            <h2>Print Service</h2>
            <p>We offer print service</p>
            <p>You can print your documents or photos</p>
            <p>Very high quality</p>
            <p><strong>(Letter size) 32 lb Premium Paper</strong></p>
            <p>Black $0.5 , Color $1</p>
            <p><strong>(Letter size) 110 lb Ultra Premium Paper</strong></p>
            <p>Black $1 , Color $2</p>
            <p><strong>(Letter size) Premium 45 lbs Photo Paper (Matte)</strong></p>
            <p>Black $1.5 , Color $3</p>
            <p><strong>(Letter size) Five Stars Premium 70 lbs Photo Paper (Glossy)</strong></p>
            <p>Black $2.5 , Color $5</p>
            <p><strong>(Letter size) 5 mil Thermal Laminating Pouch</strong> $5</p>
            <p><strong>4"x 6" Five Stars Premium 70 lbs Photo Paper</strong></p>
            <p>Black & Color $1 each</p>
            <p><strong>4"x6" 5 mil Thermal Laminating Pouch</strong> $3</p>
            <p>Price includes service fee</p>
            <p>Shipping & handling fee = $3 ~ $10 based on volume and paper size</p>
            <p>10% discount applied when you print over 10 papers</p>
            <p><strong>Beautiful Photo Album for sale</strong></p>
            <p>Basic Photo Album 36 Photos = $5</p>
            <p>Premium Hard Cover Pink Flower 52 Photos = $15 each</p>
            <p>Premium Hard Cover Pink Flower 300 Photos = $35 each</p>
            <p>Premium Hard Cover Pink Flower 600 Photos = $55 each</p>
            <p><strong>Notice :</strong> Price can be changed if I need to adjust.</p>
            <p><strong>Privacy Policy</strong></p>
            <p>All your documents or photos we print will be deleted after the print job is completed.</p>
            <p>We do not keep backup.</p>
        </div>
        <div id="website-service" class="section">
            <h2>All in 1 Website Service</h2>
            <p>I can design a website that can be used as your business presentation.</p>
            <p>All you need to do is to provide your business info.</p>
            <p>I will design both desktop and mobile version websites.</p>
            <p>Choose your own URL name and host your website from our server.</p>
            <p>You just need to pay a monthly payment (price varies) to keep your website running.</p>
        </div>
        <div id="question-consult" class="section">
            <h2>Question Consult</h2>
            <p>You can ask questions for the answers. Price vary depend on complexity. contact me for details.</p>
        </div>
        <div id="useful-softwares" class="section">
            <h2>Useful PC Softwares & iOS Apps</h2>
            <p>Our service is to introduce the best PC softwares or iOS Apps for your works or entertainments.</p>
            <p><strong>PS:</strong> Some softwares against our services cannot be offered.</p>
            <p>Price vary depending on software value.</p>
            <p>Base price is $60~$200 introduction fee.</p>
            <p>Introduction fee includes basic tutorial if you need.</p>
            <p><strong>Optional:</strong></p>
            <p>In-home installation fee = $4 per mile + $30</p>
            <p>Software license fee = License + $30</p>
            <p>Or Monthly license = $5 per month</p>
        </div>
        <div id="best-music" class="section">
            <h2>Best Music Player</h2>
            <p>I can install the best music player for you.</p>
            <p><strong>Price</strong></p>
            <p>Installation fee = $100</p>
            <p>Month Premium Subscription fee = $18</p>
        </div>
        <div id="copyright" class="section">
            <h2>All rights strictly reserved</h2>
            <p>Copyright 2025</p>
            <p>KelvinKMS.com</p>
        </div>
    </div>
    <div class="nav">
        <button onclick="scrollToSection('home')">Home</button>
        <button onclick="scrollToSection('portfolio')">VIP Custom PC</button>
        <button onclick="scrollToSection('optimize')">Optimize Photo & Video</button>
        <button onclick="scrollToSection('print-service')">Print Service</button>
        <button onclick="scrollToSection('website-service')">All in 1 Website</button>
        <button onclick="scrollToSection('question-consult')">Question Consult</button>
        <button onclick="scrollToSection('useful-softwares')">Useful Softwares & Apps</button>
        <button onclick="scrollToSection('best-music')">Best Music Player</button>
    </div>
    <div class="mobile-nav">
        <button onclick="scrollToSection('home')">🏠</button>
    </div>

    <script src="custom-modal.js"></script>
    <script>
        // --- Original Scripts ---
        function scrollToSection(id) {
            document.getElementById(id).scrollIntoView({ behavior: "smooth" });
        }

        let slideIndex = 0;
        showSlides();

        function showSlides() {
            let slides = document.getElementsByClassName("slide");
            if (slides.length > 0) {
                for (let i = 0; i < slides.length; i++) {
                    slides[i].style.display = "none";
                }
                slideIndex++;
                if (slideIndex > slides.length) { slideIndex = 1 }
                slides[slideIndex - 1].style.display = "block";
                setTimeout(showSlides, 3000);
            }
        }

        // --- New Scripts for Counter, Auth, and Modals ---
        document.addEventListener('DOMContentLoaded', function() {
            // Page view counter - get from server
            fetch('get_view_count.php')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('counter').innerText = data.count;
                })
                .catch(error => {
                    console.error('Error fetching view count:', error);
                    document.getElementById('counter').innerText = '0';
                });

            // Get modals
            const registerModal = document.getElementById('registerModal');
            const loginModal = document.getElementById('loginModal');

            // Get buttons that open modals
            const registerBtn = document.getElementById('registerBtn');
            const loginBtn = document.getElementById('loginBtn');

            // Get close buttons
            const closeRegister = document.getElementById('closeRegister');
            const closeLogin = document.getElementById('closeLogin');

            // Open modals
            registerBtn.onclick = () => registerModal.style.display = 'block';
            loginBtn.onclick = () => loginModal.style.display = 'block';

            // Close modals
            closeRegister.onclick = () => registerModal.style.display = 'none';
            closeLogin.onclick = () => loginModal.style.display = 'none';
            window.onclick = function(event) {
                if (event.target == registerModal) registerModal.style.display = 'none';
                if (event.target == loginModal) loginModal.style.display = 'none';
            }

            // --- Authentication Logic (using PHP backend) ---
            const registerForm = document.getElementById('registerForm');
            registerForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get all form values
                const firstName = document.getElementById('regFirstName').value.trim();
                const lastName = document.getElementById('regLastName').value.trim();
                const nickname = document.getElementById('regNickname').value.trim();
                const username = document.getElementById('regUsername').value.trim();
                const gender = document.getElementById('regGender').value;
                const birthday = document.getElementById('regBirthday').value;
                const language = document.getElementById('regLanguage').value;
                const email = document.getElementById('regEmail').value.trim();
                const emailConfirm = document.getElementById('regEmailConfirm').value.trim();
                const phone = document.getElementById('regPhone').value.trim();
                const password = document.getElementById('regPassword').value;
                const passwordConfirm = document.getElementById('regPasswordConfirm').value;

                // Validation
                if (!firstName || !lastName) {
                    showError('Invalid Name', 'First name and last name are required.');
                    return;
                }
                if (username.length < 3) {
                    showError('Invalid Username', 'Username must be at least 3 characters long.');
                    return;
                }
                if (!gender) {
                    showError('Invalid Gender', 'Please select your gender.');
                    return;
                }
                if (!birthday) {
                    showError('Invalid Birthday', 'Please enter your birthday.');
                    return;
                }
                if (email !== emailConfirm) {
                    showError('Email Mismatch', 'Email addresses do not match.');
                    return;
                }
                if (!phone) {
                    showError('Invalid Phone', 'Phone number is required.');
                    return;
                }
                if (password.length < 6) {
                    showError('Invalid Password', 'Password must be at least 6 characters long.');
                    return;
                }
                if (password !== passwordConfirm) {
                    showError('Password Mismatch', 'Passwords do not match.');
                    return;
                }

                // Show loading
                showLoading('Creating Account', 'Please wait while we create your account...');

                // Send registration data to server
                const formData = new FormData();
                formData.append('first_name', firstName);
                formData.append('last_name', lastName);
                formData.append('nickname', nickname);
                formData.append('username', username);
                formData.append('gender', gender);
                formData.append('birthday', birthday);
                formData.append('language', language);
                formData.append('email', email);
                formData.append('phone_number', phone);
                formData.append('password', password);

                fetch('register.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    hideModal();
                    if (data.success) {
                        registerModal.style.display = 'none';
                        loginModal.style.display = 'block';
                        registerForm.reset();
                        showSuccess('Registration Successful', 'Your account has been created! Please login with your credentials.');
                    } else {
                        showError('Registration Failed', data.message || 'Registration failed. Please try again.');
                    }
                })
                .catch(error => {
                    hideModal();
                    console.error('Error:', error);
                    showError('Registration Failed', 'Network error. Please check your connection and try again.');
                });
            });

            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;

                // Show loading
                showLoading('Logging In', 'Please wait while we verify your credentials...');

                // Send login data to server
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);

                fetch('login.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    hideModal();
                    if (data.success) {
                        // Direct redirect without alert
                        if (data.is_admin) {
                            window.location.href = 'admin.php';
                        } else {
                            window.location.href = 'member.php';
                        }
                    } else {
                        showError('Login Failed', data.message || 'Invalid username or password.');
                    }
                })
                .catch(error => {
                    hideModal();
                    console.error('Error:', error);
                    showError('Login Failed', 'Network error. Please check your connection and try again.');
                });
            });
        });
    </script>
</body>
</html>